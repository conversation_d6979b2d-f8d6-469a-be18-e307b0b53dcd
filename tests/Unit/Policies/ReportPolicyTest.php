<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\User;
use App\Policies\ReportPolicy;
use App\Services\PermissionService;
use App\Services\ProductPermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Report Policy Test
 *
 * Comprehensive tests for report access permissions including:
 * - Basic role-based access control
 * - Organization-specific authorization
 * - Security validations
 * - Performance optimizations
 */
final class ReportPolicyTest extends TestCase
{
    use RefreshDatabase;

    private ReportPolicy $policy;
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unrelatedUser;
    private User $visitorUser;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Product $product;
    private Product $anotherProduct;
    private Role $systemRootRole;
    private Role $systemAdminRole;
    private Role $ownerRole;
    private Role $memberRole;
    private ProductPermissionService $productPermissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new ReportPolicy();

        // Create organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $this->systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        $this->systemAdminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create organisation roles
        $this->ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create products
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'owner_id' => $this->organisation->code,
        ]);

        $this->anotherProduct = Product::factory()->create([
            'name' => 'Another Product',
            'owner_id' => $this->anotherOrganisation->code,
        ]);

        // Create users
        $this->systemRootUser = User::factory()->create(['name' => 'System Root']);
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->unrelatedUser = User::factory()->create(['name' => 'Unrelated User']);
        $this->visitorUser = User::factory()->create(['name' => 'Visitor User']);

        // Initialize product permission service
        $this->productPermissionService = app(ProductPermissionService::class);

        // Assign system roles (need to use system guard)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);
        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_system_root_can_view_any_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_system_admin_can_view_any_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_organisation_owner_can_view_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
    }

    public function test_organisation_member_can_view_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->memberUser));
    }

    public function test_unrelated_user_cannot_view_reports(): void
    {
        $this->assertFalse($this->policy->viewAny($this->unrelatedUser));
    }

    // ========================================
    // viewForOrganisations Tests
    // ========================================

    public function test_system_root_can_view_any_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisation($this->systemRootUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisation($this->systemRootUser, $this->anotherOrganisation->id));
        $this->assertTrue($this->policy->viewForOrganisation($this->systemRootUser, null)); // Can access without specifying organisation
    }

    public function test_system_admin_can_view_any_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, $this->anotherOrganisation->id));
        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, null)); // Can access without specifying organisation
    }

    public function test_organisation_owner_can_view_own_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisation($this->ownerUser, $this->organisation->id));
    }

    public function test_organisation_owner_cannot_view_other_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisation($this->ownerUser, $this->anotherOrganisation->id));
    }

    public function test_organisation_owner_cannot_access_without_organisation_id(): void
    {
        $this->assertFalse($this->policy->viewForOrganisation($this->ownerUser, null));
    }

    public function test_organisation_member_can_view_own_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisation($this->memberUser, $this->organisation->id));
    }

    public function test_organisation_member_cannot_view_other_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisation($this->memberUser, $this->anotherOrganisation->id));
    }

    public function test_organisation_member_cannot_access_without_organisation_id(): void
    {
        $this->assertFalse($this->policy->viewForOrganisation($this->memberUser, null));
    }

    public function test_unrelated_user_cannot_view_any_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisation($this->unrelatedUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisation($this->unrelatedUser, $this->anotherOrganisation->id));
        $this->assertFalse($this->policy->viewForOrganisation($this->unrelatedUser, null));
    }







    public function test_exportForOrganisation_respects_export_restrictions(): void
    {
        // System admins can export for any organisation
        $this->assertTrue($this->policy->exportForOrganisation($this->systemRootUser, (string) $this->organisation->id));
        $this->assertTrue($this->policy->exportForOrganisation($this->systemAdminUser, (string) $this->organisation->id));

        // Organisation owners can export for their own organisation
        $this->assertTrue($this->policy->exportForOrganisation($this->ownerUser, (string) $this->organisation->id));
        $this->assertFalse($this->policy->exportForOrganisation($this->ownerUser, (string) $this->anotherOrganisation->id));

        // Organisation members can export
        $this->assertTrue($this->policy->exportForOrganisation($this->memberUser, (string) $this->organisation->id));

        // Unrelated users cannot export
        $this->assertFalse($this->policy->exportForOrganisation($this->unrelatedUser, (string) $this->organisation->id));
    }

    // ========================================
    // Optimization Tests
    // ========================================

    public function test_all_report_types_follow_same_authorization_pattern(): void
    {
        // Test that all report types follow the same authorization pattern using viewForOrganisation
        $organisationId = $this->organisation->id;

        // System admin should have access to all report types
        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, $organisationId));

        // Owner should have access to all report types for their organisation
        $this->assertTrue($this->policy->viewForOrganisation($this->ownerUser, $organisationId));

        // Member should have access to all report types for their organisation
        $this->assertTrue($this->policy->viewForOrganisation($this->memberUser, $organisationId));

        // Unrelated user should not have access to any report types
        $this->assertFalse($this->policy->viewForOrganisation($this->unrelatedUser, $organisationId));
    }

    // ========================================
    // Security Tests
    // ========================================

    public function test_viewAny_with_organization_id_validates_access(): void
    {
        // System admin should be able to access reports for any organization
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser, 'report', null));
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser, 'report', $this->organisation->id));
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser, 'report', $this->anotherOrganisation->id));

        // Org owner should be able to access reports for their org
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
        $this->assertTrue($this->policy->viewAny($this->ownerUser, 'report', $this->organisation->id));

        // Org member should be able to access reports for their org
        $this->assertTrue($this->policy->viewAny($this->memberUser));
        $this->assertTrue($this->policy->viewAny($this->memberUser, 'report', $this->organisation->id));
    }

    public function test_user_cannot_access_reports_for_other_organizations(): void
    {
        // Org owner should NOT be able to access reports for other org
        $this->assertFalse($this->policy->viewAny($this->ownerUser, 'report', $this->anotherOrganisation->id));

        // Org member should NOT be able to access reports for other org
        $this->assertFalse($this->policy->viewAny($this->memberUser, 'report', $this->anotherOrganisation->id));
    }

    public function test_security_fix_prevents_unauthorized_access(): void
    {
        // This test specifically validates the security fix
        // Before the fix: viewAny only checked if user belonged to ANY organization
        // After the fix: viewAny validates access to SPECIFIC organizations when provided

        // Create a user with no organizations
        $userWithoutOrg = User::factory()->create();

        // User without organizations should not have access
        $this->assertFalse($this->policy->viewAny($userWithoutOrg, 'report', null));
        $this->assertFalse($this->policy->viewAny($userWithoutOrg, 'report', $this->organisation->id));
        $this->assertFalse($this->policy->viewAny($userWithoutOrg, 'report', $this->anotherOrganisation->id));

        // Org owner trying to access other org data should be denied
        $this->assertFalse($this->policy->viewAny($this->ownerUser, 'report', $this->anotherOrganisation->id));

        // Org member trying to access other org data should be denied
        $this->assertFalse($this->policy->viewAny($this->memberUser, 'report', $this->anotherOrganisation->id));
    }

    public function test_viewAny_general_access_maintained(): void
    {
        // Ensure that existing calls without organization IDs still work
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
        $this->assertTrue($this->policy->viewAny($this->memberUser));
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
        $this->assertFalse($this->policy->viewAny($this->unrelatedUser));
    }

    public function test_viewForOrganisation_method_works_correctly(): void
    {
        // Test that the core organization method works correctly
        $this->assertTrue($this->policy->viewForOrganisation($this->ownerUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisation($this->ownerUser, $this->anotherOrganisation->id));

        $this->assertTrue($this->policy->viewForOrganisation($this->memberUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisation($this->memberUser, $this->anotherOrganisation->id));

        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisation($this->systemAdminUser, $this->anotherOrganisation->id));
    }

    // ========================================
    // Product Permission Tests (New)
    // ========================================

    public function test_viewForProduct_allows_access_with_product_permission(): void
    {
        // Grant product permission to visitor user
        $this->productPermissionService->grantProductAccess($this->visitorUser, $this->product);

        // Visitor user should be able to access the specific product
        $this->assertTrue($this->policy->viewForProduct($this->visitorUser, $this->product->id));
    }

    public function test_viewForProduct_denies_access_without_product_permission(): void
    {
        // Visitor user without product permission should not have access
        $this->assertFalse($this->policy->viewForProduct($this->visitorUser, $this->product->id));
    }

    public function test_viewForProduct_allows_organization_member_access(): void
    {
        // Organization member should have access to products in their organization
        $this->assertTrue($this->policy->viewForProduct($this->memberUser, $this->product->id));
        $this->assertTrue($this->policy->viewForProduct($this->ownerUser, $this->product->id));
    }

    public function test_viewForProduct_denies_cross_organization_access(): void
    {
        // Organization member should not have access to products in other organizations
        $this->assertFalse($this->policy->viewForProduct($this->memberUser, $this->anotherProduct->id));
        $this->assertFalse($this->policy->viewForProduct($this->ownerUser, $this->anotherProduct->id));
    }

    public function test_viewForProduct_allows_system_admin_access(): void
    {
        // System administrators should have access to all products
        $this->assertTrue($this->policy->viewForProduct($this->systemAdminUser, $this->product->id));
        $this->assertTrue($this->policy->viewForProduct($this->systemAdminUser, $this->anotherProduct->id));
        $this->assertTrue($this->policy->viewForProduct($this->systemRootUser, $this->product->id));
        $this->assertTrue($this->policy->viewForProduct($this->systemRootUser, $this->anotherProduct->id));
    }

    // ========================================
    // Enhanced Organization/Product Permission Tests (New)
    // ========================================

    public function test_viewForOrganisationOrProducts_allows_system_admin_access(): void
    {
        // System administrators should have access to all organizations
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->systemAdminUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->systemAdminUser, $this->anotherOrganisation->id));
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->systemRootUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->systemRootUser, $this->anotherOrganisation->id));
    }

    public function test_viewForOrganisationOrProducts_denies_access_without_organisation_id(): void
    {
        // Should deny access if no organization ID is specified
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->memberUser, null));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->ownerUser, null));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->visitorUser, null));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->unrelatedUser, null));
    }

    public function test_viewForOrganisationOrProducts_allows_organization_member_access(): void
    {
        // Organization members should have access to their organization's reports
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->memberUser, $this->organisation->id));
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->ownerUser, $this->organisation->id));
    }

    public function test_viewForOrganisationOrProducts_denies_cross_organization_access(): void
    {
        // Organization members should not have access to other organizations
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->memberUser, $this->anotherOrganisation->id));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->ownerUser, $this->anotherOrganisation->id));
    }

    public function test_viewForOrganisationOrProducts_allows_visitor_with_product_permission(): void
    {
        // Grant product permission to visitor user for a product in the organization
        $this->productPermissionService->grantProductAccess($this->visitorUser, $this->product);

        // Visitor user should have access to the organization's reports due to product permission
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->visitorUser, $this->organisation->id));
    }

    public function test_viewForOrganisationOrProducts_denies_visitor_without_product_permission(): void
    {
        // Visitor user without product permission should not have access
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->visitorUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->visitorUser, $this->anotherOrganisation->id));
    }

    public function test_viewForOrganisationOrProducts_prevents_cross_organization_product_access(): void
    {
        // Grant product permission to visitor user for a product in organization A
        $this->productPermissionService->grantProductAccess($this->visitorUser, $this->product);

        // Visitor should have access to organization A but not organization B
        $this->assertTrue($this->policy->viewForOrganisationOrProducts($this->visitorUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->visitorUser, $this->anotherOrganisation->id));
    }

    public function test_viewForOrganisationOrProducts_denies_unrelated_user_access(): void
    {
        // Unrelated user should not have access to any organization
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->unrelatedUser, $this->organisation->id));
        $this->assertFalse($this->policy->viewForOrganisationOrProducts($this->unrelatedUser, $this->anotherOrganisation->id));
    }
}
